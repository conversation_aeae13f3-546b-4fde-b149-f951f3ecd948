import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useDropzone } from 'react-dropzone';
import toast from 'react-hot-toast';
import {
  DocumentTextIcon,
  CloudArrowUpIcon,
  TrashIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
  ChartBarIcon,
  DocumentIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

const Sidebar = ({ 
  isOpen, 
  documents, 
  selectedDocument, 
  onDocumentSelect, 
  onDocumentUpload, 
  onDocumentDelete,
  onChatClick,
  onAnalyticsClick,
  onViewClick
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadSettings, setUploadSettings] = useState({
    include_marginalia: true,
    include_metadata: true,
    save_groundings: false
  });

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.png', '.jpg', '.jpeg', '.tiff', '.bmp']
    },
    multiple: true,
    onDrop: handleFileUpload
  });

  async function handleFileUpload(acceptedFiles) {
    if (acceptedFiles.length === 0) return;

    setIsUploading(true);
    
    for (const file of acceptedFiles) {
      try {
        toast.loading(`Processing ${file.name}...`, { id: file.name });
        await onDocumentUpload(file, uploadSettings);
        toast.success(`${file.name} processed successfully!`, { id: file.name });
      } catch (error) {
        toast.error(`Failed to process ${file.name}: ${error.message}`, { id: file.name });
      }
    }
    
    setIsUploading(false);
  }

  const handleDelete = async (documentId, filename) => {
    if (window.confirm(`Are you sure you want to delete "${filename}"?`)) {
      try {
        await onDocumentDelete(documentId);
        toast.success('Document deleted successfully');
      } catch (error) {
        toast.error('Failed to delete document');
      }
    }
  };

  const formatFileSize = (stats) => {
    if (!stats) return '';
    return `${stats.total_chunks} chunks`;
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.aside
          initial={{ x: -320 }}
          animate={{ x: 0 }}
          exit={{ x: -320 }}
          transition={{ type: "spring", damping: 25, stiffness: 200 }}
          className="fixed left-0 top-16 bottom-0 w-80 backdrop-blur-xl bg-black/20 border-r border-white/10 z-40"
        >
          <div className="flex flex-col h-full">
            {/* Upload Section */}
            <div className="p-6 border-b border-white/10">
              <div className="flex items-center space-x-2 mb-4">
                <SparklesIcon className="w-5 h-5 text-blue-400" />
                <h2 className="text-lg font-semibold text-white">
                  Upload Documents
                </h2>
              </div>
              
              <motion.div
                {...getRootProps()}
                className={`
                  border-2 border-dashed rounded-xl p-6 text-center cursor-pointer transition-all duration-300
                  ${isDragActive 
                    ? 'border-blue-400 bg-blue-400/10 scale-105' 
                    : 'border-white/20 hover:border-white/30 hover:bg-white/5'
                  }
                `}
                whileHover={{ scale: isDragActive ? 1.05 : 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <input {...getInputProps()} />
                <motion.div
                  animate={isDragActive ? { y: [0, -5, 0] } : {}}
                  transition={{ duration: 0.5, repeat: isDragActive ? Infinity : 0 }}
                >
                  <CloudArrowUpIcon className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                </motion.div>
                <p className="text-white mb-2">
                  {isDragActive 
                    ? 'Drop files here...' 
                    : 'Drag & drop files or click to browse'
                  }
                </p>
                <p className="text-xs text-gray-400">
                  PDF, PNG, JPG, TIFF, BMP
                </p>
              </motion.div>

              {/* Upload Settings */}
              <motion.div 
                className="mt-4 space-y-2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <label className="flex items-center space-x-2 text-sm text-gray-300 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={uploadSettings.include_marginalia}
                    onChange={(e) => setUploadSettings(prev => ({
                      ...prev,
                      include_marginalia: e.target.checked
                    }))}
                    className="rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-800"
                  />
                  <span>Include headers/footers</span>
                </label>
                
                <label className="flex items-center space-x-2 text-sm text-gray-300 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={uploadSettings.include_metadata}
                    onChange={(e) => setUploadSettings(prev => ({
                      ...prev,
                      include_metadata: e.target.checked
                    }))}
                    className="rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-800"
                  />
                  <span>Include metadata</span>
                </label>
                
                <label className="flex items-center space-x-2 text-sm text-gray-300 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={uploadSettings.save_groundings}
                    onChange={(e) => setUploadSettings(prev => ({
                      ...prev,
                      save_groundings: e.target.checked
                    }))}
                    className="rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500 focus:ring-offset-gray-800"
                  />
                  <span>Save visual groundings</span>
                </label>
              </motion.div>
            </div>

            {/* Documents List */}
            <div className="flex-1 overflow-y-auto custom-scroll">
              <div className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-md font-medium text-white">
                    Documents ({documents.length})
                  </h3>
                </div>

                {documents.length === 0 ? (
                  <motion.div 
                    className="text-center py-8"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                  >
                    <DocumentIcon className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                    <p className="text-gray-400 text-sm">
                      No documents uploaded yet
                    </p>
                  </motion.div>
                ) : (
                  <div className="space-y-2">
                    {documents.map((doc, index) => (
                      <motion.div
                        key={doc.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className={`
                          group relative p-3 rounded-xl border cursor-pointer transition-all duration-300
                          ${selectedDocument?.id === doc.id
                            ? 'bg-blue-500/20 border-blue-500/50 shadow-lg shadow-blue-500/20'
                            : 'bg-white/5 border-white/10 hover:bg-white/10 hover:border-white/20'
                          }
                        `}
                        onClick={() => onDocumentSelect(doc)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-start space-x-3">
                          <motion.div
                            className="mt-0.5"
                            whileHover={{ rotate: 5 }}
                          >
                            <DocumentTextIcon className="w-5 h-5 text-blue-400" />
                          </motion.div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-white truncate">
                              {doc.filename}
                            </p>
                            <div className="flex items-center space-x-2 mt-1">
                              <span className="text-xs text-gray-400">
                                {formatFileSize(doc.stats)}
                              </span>
                              <span className="text-xs text-gray-500">•</span>
                              <span className="text-xs text-gray-500">
                                {new Date(doc.extraction_time).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          
                          <motion.button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(doc.id, doc.filename);
                            }}
                            className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-400 transition-all duration-200"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            <TrashIcon className="w-4 h-4" />
                          </motion.button>
                        </div>

                        {/* Document Actions */}
                        {selectedDocument?.id === doc.id && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            className="mt-3 pt-3 border-t border-white/10 flex space-x-2"
                          >
                            <motion.button 
                              onClick={() => onViewClick(doc)}
                              className="flex-1 flex items-center justify-center space-x-1 py-2 px-3 text-xs bg-white/10 hover:bg-white/20 text-gray-200 rounded-lg transition-colors"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <EyeIcon className="w-3 h-3" />
                              <span>View</span>
                            </motion.button>
                            <motion.button 
                              onClick={() => onChatClick(doc)}
                              className="flex-1 flex items-center justify-center space-x-1 py-2 px-3 text-xs bg-white/10 hover:bg-white/20 text-gray-200 rounded-lg transition-colors"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <ChatBubbleLeftRightIcon className="w-3 h-3" />
                              <span>Chat</span>
                            </motion.button>
                            <motion.button 
                              onClick={() => onAnalyticsClick(doc)}
                              className="flex-1 flex items-center justify-center space-x-1 py-2 px-3 text-xs bg-white/10 hover:bg-white/20 text-gray-200 rounded-lg transition-colors"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <ChartBarIcon className="w-3 h-3" />
                              <span>Stats</span>
                            </motion.button>
                          </motion.div>
                        )}

                        {/* Selection Indicator */}
                        {selectedDocument?.id === doc.id && (
                          <motion.div
                            className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-blue-400 to-purple-400 rounded-r"
                            initial={{ scaleY: 0 }}
                            animate={{ scaleY: 1 }}
                            transition={{ duration: 0.3 }}
                          />
                        )}
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Upload Progress */}
            <AnimatePresence>
              {isUploading && (
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 50 }}
                  className="p-4 border-t border-white/10 backdrop-blur-sm bg-black/30"
                >
                  <div className="flex items-center space-x-3">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full"
                    />
                    <span className="text-sm text-gray-300">Processing documents...</span>
                  </div>
                  <motion.div
                    className="mt-2 h-1 bg-gray-700 rounded-full overflow-hidden"
                    initial={{ width: 0 }}
                    animate={{ width: "100%" }}
                  >
                    <motion.div
                      className="h-full bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"
                      animate={{ x: ["-100%", "100%"] }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
                    />
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.aside>
      )}
    </AnimatePresence>
  );
};

export default Sidebar;