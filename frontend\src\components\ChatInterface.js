import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  PaperAirplaneIcon,
  TrashIcon,
  LightBulbIcon,
  ChatBubbleLeftRightIcon,
  UserIcon,
  CpuChipIcon,
  CheckCircleIcon,
  ClockIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

const ChatInterface = ({ document, apiStatus, apiService }) => {
  const [messages, setMessages] = useState([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);

  // Define smartPrompts at the top of the component
  const smartPrompts = {
    "Quick Analysis": [
      "What are the key points in this document?",
      "Summarize the main findings",
      "What are the most important sections?",
      "Extract key statistics and numbers"
    ],
    "Content Discovery": [
      "What topics are covered?",
      "Are there any recommendations?",
      "What are the conclusions?",
      "Identify any action items"
    ]
  };

  // Move loadChatHistory above useEffect so it is not a missing dependency
  const loadChatHistory = async () => {
    try {
      if (apiService && apiService.getChatHistory) {
        const history = await apiService.getChatHistory(document.id);
        setMessages(history || []);
      }
    } catch (error) {
      console.error('Failed to load chat history:', error);
      setMessages([]);
    }
  };

  useEffect(() => {
    if (document) {
      loadChatHistory();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [document]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (message = currentMessage) => {
    if (!message.trim() || isLoading) return;

    const userMessage = {
      question: message,
      answer: '',
      timestamp: new Date().toLocaleTimeString(),
      confidence: 0,
      sources: [],
      chunk_references: [],
      isUser: true
    };

    setMessages(prev => [...prev, userMessage]);
    setCurrentMessage('');
    setIsLoading(true);

    try {
      let response;
      if (apiService && apiService.chatWithDocument) {
        response = await apiService.chatWithDocument(document.id, message, 'openai');
        // Ensure response is not undefined and has an answer
        if (response && response.answer) {
          setMessages(prev => [...prev, { ...response, isUser: false }]);
        } else {
          setMessages(prev => [...prev, {
            question: message,
            answer: 'No answer received from AI.',
            timestamp: new Date().toLocaleTimeString(),
            confidence: 0,
            sources: [],
            chunk_references: [],
            isUser: false
          }]);
        }
      } else {
        // Fallback mock response for demo
        await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API delay
        response = {
          question: message,
          answer: `This is a mock response to your question: "${message}". In a real implementation, this would be generated by AI based on the document content.`,
          timestamp: new Date().toLocaleTimeString(),
          confidence: 0.85,
          sources: ['Document chunk 1', 'Document chunk 3'],
          chunk_references: [1, 3],
          isUser: false
        };
        setMessages(prev => [...prev, response]);
      }
    } catch (error) {
      console.error('Failed to get response:', error);
      const errorMessage = {
        question: message,
        answer: 'Sorry, I encountered an error while processing your question. Please try again.',
        timestamp: new Date().toLocaleTimeString(),
        confidence: 0,
        sources: [],
        chunk_references: [],
        isUser: false
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearChat = async () => {
    if (window.confirm('Are you sure you want to clear the chat history?')) {
      try {
        if (apiService && apiService.clearChatHistory) {
          await apiService.clearChatHistory(document.id);
        }
        setMessages([]);
      } catch (error) {
        console.error('Failed to clear chat history:', error);
        // Clear locally even if API fails
        setMessages([]);
      }
    }
  };

  const handlePromptClick = (prompt) => {
    setCurrentMessage(prompt);
    // Only set the input, do not send immediately
  };

  const getConfidenceColor = (confidence) => {
    if (confidence > 0.7) return 'text-green-500';
    if (confidence > 0.4) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getConfidenceIcon = (confidence) => {
    if (confidence > 0.7) return <CheckCircleIcon className="w-4 h-4" />;
    if (confidence > 0.4) return <ClockIcon className="w-4 h-4" />;
    return <CheckCircleIcon className="w-4 h-4 opacity-50" />;
  };

  if (!document) {
    return (
      <div className="flex items-center justify-center h-screen bg-gradient-to-br from-black via-gray-900/50 to-black relative overflow-hidden">
        {/* Background Effects */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 2, ease: "easeOut" }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 2.5, ease: "easeOut", delay: 0.5 }}
        />

        <motion.div 
          className="text-center relative z-10"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.div
            className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl mb-6 shadow-xl"
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ duration: 0.8, type: "spring", damping: 20 }}
          >
            <ChatBubbleLeftRightIcon className="w-8 h-8 text-white" />
          </motion.div>
          <h2 className="text-2xl font-bold text-white mb-2">AI Chat Interface</h2>
          <p className="text-gray-400">Select a document to start an intelligent conversation</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-black via-gray-900/30 to-black relative overflow-hidden">
      {/* Background Effects */}
      <motion.div
        className="absolute top-20 right-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 2, ease: "easeOut" }}
      />
      <motion.div
        className="absolute bottom-40 left-20 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl"
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 2.5, ease: "easeOut", delay: 0.5 }}
      />

      {/* Header */}
      <motion.div 
        className="flex-shrink-0 backdrop-blur-xl bg-black/20 border-b border-white/10 p-4 relative z-10"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <motion.div
              className="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", damping: 15 }}
            >
              <ChatBubbleLeftRightIcon className="w-6 h-6 text-white" />
            </motion.div>
            <div>
              <h2 className="text-lg font-semibold text-white">Chat with Document</h2>
              <p className="text-sm text-gray-400 truncate max-w-md">
                {document.filename}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <motion.div
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-full px-3 py-1 backdrop-blur-sm"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-green-300 text-xs font-medium">AI Ready</span>
            </motion.div>
            
            <motion.button
              onClick={handleClearChat}
              className="p-2 text-gray-400 hover:text-red-400 hover:bg-white/10 rounded-lg transition-colors"
              title="Clear chat history"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <TrashIcon className="w-5 h-5" />
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* Smart Prompts */}
      {Object.keys(smartPrompts).length > 0 && messages.length === 0 && (
        <motion.div 
          className="flex-shrink-0 p-4 backdrop-blur-xl bg-blue-500/5 border-b border-blue-500/10 relative z-10"
          initial={{ y: -30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="flex items-center space-x-2 mb-3">
            <LightBulbIcon className="w-5 h-5 text-blue-400" />
            <span className="text-sm font-medium text-blue-300">Smart Question Suggestions</span>
          </div>
          
          <div className="space-y-3">
            {Object.entries(smartPrompts).map(([category, prompts], categoryIndex) => (
              <motion.div 
                key={category}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: categoryIndex * 0.1 }}
              >
                <h4 className="text-xs font-medium text-blue-400 mb-2">{category}</h4>
                <div className="flex flex-wrap gap-2">
                  {prompts.slice(0, 4).map((prompt, index) => (
                    <motion.button
                      key={index}
                      onClick={() => handlePromptClick(prompt)}
                      className="px-3 py-1 text-xs bg-white/10 hover:bg-blue-500/20 text-blue-200 border border-blue-500/20 rounded-full transition-all duration-300 backdrop-blur-sm"
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3, delay: categoryIndex * 0.1 + index * 0.05 }}
                    >
                      {prompt}
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 relative z-10">
        <AnimatePresence>
          {messages.map((message, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.5, type: "spring", damping: 25 }}
              className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`
                max-w-3/4 flex space-x-3 ${message.isUser ? 'flex-row-reverse space-x-reverse' : ''}
              `}>
                {/* Avatar */}
                <motion.div 
                  className={`
                    flex-shrink-0 w-10 h-10 rounded-xl flex items-center justify-center shadow-lg
                    ${message.isUser ? 'bg-gradient-to-r from-blue-500 to-purple-600' : 'bg-white/10 backdrop-blur-sm border border-white/20'}
                  `}
                  initial={{ scale: 0, rotate: -90 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                >
                  {message.isUser ? (
                    <UserIcon className="w-5 h-5 text-white" />
                  ) : (
                    <CpuChipIcon className="w-5 h-5 text-blue-400" />
                  )}
                </motion.div>

                {/* Message Content */}
                <motion.div 
                  className={`
                    flex-1 rounded-2xl p-4 backdrop-blur-xl shadow-lg border transition-all duration-300
                    ${message.isUser 
                      ? 'bg-gradient-to-r from-blue-500/20 to-purple-600/20 border-blue-500/30 text-white' 
                      : 'bg-white/5 border-white/10 hover:bg-white/10'
                    }
                  `}
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.4, delay: index * 0.1 + 0.1 }}
                >
                  {message.isUser ? (
                    <p className="text-white">{message.question}</p>
                  ) : (
                    <div>
                      <div className="prose prose-sm max-w-none">
                        <div className="text-gray-200 whitespace-pre-wrap">
                          {message.answer}
                        </div>
                      </div>
                      
                      {/* Metadata */}
                      <motion.div 
                        className="mt-3 pt-3 border-t border-white/10"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.5 }}
                      >
                        <div className="flex items-center justify-between text-xs">
                          <div className="flex items-center space-x-4">
                            <div className={`flex items-center space-x-1 ${getConfidenceColor(message.confidence || 0.8)}`}>
                              {getConfidenceIcon(message.confidence || 0.8)}
                              <span>
                                {((message.confidence || 0.8) * 100).toFixed(0)}% confidence
                              </span>
                            </div>
                            <span className="text-gray-400">{message.timestamp || new Date().toLocaleTimeString()}</span>
                          </div>
                          
                          {message.sources && message.sources.length > 0 && (
                            <button className="text-gray-500 hover:text-gray-300 transition-colors">
                              {message.sources.length} source{message.sources.length !== 1 ? 's' : ''}
                            </button>
                          )}
                        </div>
                        
                        {/* Sources */}
                        {message.sources && message.sources.length > 0 && (
                          <motion.div 
                            className="mt-2"
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            transition={{ duration: 0.3 }}
                          >
                            <details className="text-xs">
                              <summary className="cursor-pointer text-gray-400 hover:text-gray-200">
                                View Sources
                              </summary>
                              <ul className="mt-2 space-y-1 text-gray-500">
                                {message.sources.map((source, idx) => (
                                  <li key={idx}>• {source}</li>
                                ))}
                              </ul>
                            </details>
                          </motion.div>
                        )}
                      </motion.div>
                    </div>
                  )}
                </motion.div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Loading indicator */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 30, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            className="flex justify-start"
          >
            <div className="flex space-x-3">
              <div className="w-10 h-10 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl flex items-center justify-center">
                <CpuChipIcon className="w-5 h-5 text-blue-400" />
              </div>
              <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-4">
                <div className="flex items-center space-x-3">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full"
                  />
                  <span className="text-blue-300 text-sm">AI is thinking...</span>
                  <motion.div 
                    className="flex space-x-1"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5 }}
                  >
                    {[0, 1, 2].map((i) => (
                      <motion.div
                        key={i}
                        className="w-1 h-1 bg-blue-400 rounded-full"
                        animate={{ y: [0, -4, 0] }}
                        transition={{
                          duration: 0.6,
                          repeat: Infinity,
                          delay: i * 0.1
                        }}
                      />
                    ))}
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <motion.div 
        className="flex-shrink-0 backdrop-blur-xl bg-black/20 border-t border-white/10 p-4 relative z-10"
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <div className="flex space-x-3">
          <div className="flex-1 relative">
            <input
              type="text"
              value={currentMessage}
              onChange={(e) => setCurrentMessage(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Ask a question about this document..."
              disabled={isLoading}
              className="w-full bg-white/5 backdrop-blur-sm border border-white/20 text-white placeholder-gray-400 rounded-xl px-4 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 transition-all duration-300"
            />
            <motion.div
              className="absolute right-3 top-1/2 transform -translate-y-1/2"
              initial={{ scale: 0 }}
              animate={{ scale: currentMessage.trim() ? 1 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <SparklesIcon className="w-4 h-4 text-blue-400" />
            </motion.div>
          </div>
          <motion.button
            onClick={() => handleSendMessage()}
            disabled={isLoading || !currentMessage.trim()}
            className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-300 flex items-center space-x-2 shadow-lg backdrop-blur-sm"
            whileHover={{ scale: isLoading || !currentMessage.trim() ? 1 : 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <PaperAirplaneIcon className="w-5 h-5" />
            <span>Send</span>
          </motion.button>
        </div>
        
        <motion.div 
          className="mt-2 text-xs text-gray-500 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 1 }}
        >
          Powered by Advanced AI • Press Enter to send
        </motion.div>
      </motion.div>
    </div>
  );
};

export default ChatInterface;