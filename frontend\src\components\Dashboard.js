import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../contexts/AuthContext';
import { 
  DocumentTextIcon,
  ChartBarIcon,
  ChatBubbleLeftRightIcon,
  CloudArrowUpIcon,
  CubeIcon,
  ClockIcon,
  SparklesIcon,
  ArrowRightIcon,
  UserIcon
} from '@heroicons/react/24/outline';

const Dashboard = ({ documents, selectedDocument, onDocumentSelect, onDocumentUpload, onChatClick, onAnalyticsClick, onViewClick }) => {
  const [isUploading, setIsUploading] = useState(false);
  
  // Use auth context with fallback for username
  const { username = 'Guest' } = useAuth() || { username: 'Guest' };
  
  const totalChunks = documents.reduce((sum, doc) => sum + (doc.stats?.total_chunks || 0), 0);
  const totalTextLength = documents.reduce((sum, doc) => sum + (doc.stats?.total_text_length || 0), 0);
  const avgComplexity = documents.length > 0 
    ? documents.reduce((sum, doc) => sum + (doc.stats?.complexity_score || 0), 0) / documents.length 
    : 0;

  const recentDocuments = documents
    .sort((a, b) => new Date(b.extraction_time) - new Date(a.extraction_time))
    .slice(0, 5);

  const handleFileUpload = async (acceptedFiles) => {
    setIsUploading(true);
    for (const file of acceptedFiles) {
      try {
        await onDocumentUpload(file, {
          include_marginalia: true,
          include_metadata: true,
          save_groundings: false
        });
      } catch (error) {
        console.error(`Failed to process ${file.name}:`, error);
      }
    }
    setIsUploading(false);
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { 
        duration: 0.6, 
        ease: "easeOut",
        type: "spring",
        damping: 25,
        stiffness: 100
      }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.2
      }
    }
  };

  if (documents.length === 0) {
    return (
      <div className="p-8 min-h-screen relative overflow-hidden">
        {/* Animated Background */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1.5 }}
        />

        {/* Background Effects */}
        <motion.div
          className="absolute top-40 left-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 2, ease: "easeOut" }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl"
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 2.5, ease: "easeOut", delay: 0.5 }}
        />

        <motion.div
          initial="hidden"
          animate="visible"
          variants={staggerContainer}
          className="max-w-4xl mx-auto relative z-10"
        >
          {/* Welcome Header with Username */}
          <motion.div className="text-center mb-12" variants={cardVariants}>
            <motion.div
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full px-4 py-2 mb-6 backdrop-blur-sm"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", damping: 20, stiffness: 300 }}
            >
              <UserIcon className="w-4 h-4 text-blue-400" />
              <span className="text-blue-300 text-sm font-medium">
                Welcome, {username}
              </span>
            </motion.div>

            <motion.h1 
              className="text-4xl md:text-5xl font-bold mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              <span className="bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                Transform Your Documents
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                Into Intelligence
              </span>
            </motion.h1>
            <motion.p 
              className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              Upload your first document to experience the power of AI-driven document processing and analysis.
            </motion.p>
          </motion.div>

          {/* Upload Area */}
          <motion.div variants={cardVariants} className="mb-12">
            {isUploading ? (
              <motion.div 
                className="relative backdrop-blur-xl bg-blue-500/10 border border-blue-500/20 rounded-2xl p-12 text-center overflow-hidden"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5"
                  animate={{ opacity: [0.3, 0.6, 0.3] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                />
                <div className="relative z-10">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="w-16 h-16 border-4 border-blue-400 border-t-transparent rounded-full mx-auto mb-4"
                  />
                  <h3 className="text-xl font-semibold text-blue-300 mb-2">
                    Processing Documents...
                  </h3>
                  <p className="text-blue-400">
                    AI is analyzing your documents. This may take a few moments.
                  </p>
                  <motion.div 
                    className="mt-6 bg-blue-900/30 rounded-full h-2 overflow-hidden max-w-md mx-auto"
                    initial={{ width: 0 }}
                    animate={{ width: "100%" }}
                    transition={{ duration: 1 }}
                  >
                    <motion.div
                      className="h-full bg-gradient-to-r from-blue-400 to-purple-400"
                      animate={{ x: ["-100%", "100%"] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    />
                  </motion.div>
                </div>
              </motion.div>
            ) : (
              <motion.div
                className="relative border-2 border-dashed rounded-2xl p-12 text-center cursor-pointer transition-all duration-300 overflow-hidden border-white/20 hover:border-white/30 hover:bg-white/5"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.multiple = true;
                  input.accept = '.pdf,.png,.jpg,.jpeg,.tiff,.bmp';
                  input.onchange = (e) => handleFileUpload([...e.target.files]);
                  input.click();
                }}
              >
                <div className="relative z-10">
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ duration: 0.6, type: "spring", damping: 20 }}
                  >
                    <CloudArrowUpIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  </motion.div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    Start Document Extraction
                  </h3>
                  <p className="text-gray-300 mb-6">
                    Drag & drop files or click to browse • PDF, PNG, JPG, TIFF, BMP
                  </p>
                  <motion.button 
                    className="group relative px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl shadow-xl hover:shadow-blue-500/25 transition-all duration-300 overflow-hidden"
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <span className="relative z-10 flex items-center space-x-2">
                      <span>Choose Files</span>
                      <ArrowRightIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
                    </span>
                  </motion.button>
                </div>
              </motion.div>
            )}
          </motion.div>

          {/* Features Grid */}
          <motion.div
            variants={staggerContainer}
            className="grid md:grid-cols-3 gap-6"
          >
            <motion.div 
              variants={cardVariants} 
              className="group relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
              whileHover={{ y: -5 }}
            >
              <motion.div
                className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl mb-4 shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", damping: 15 }}
              >
                <DocumentTextIcon className="w-6 h-6 text-white" />
              </motion.div>
              <h3 className="text-lg font-semibold text-white mb-2">Document Processing</h3>
              <ul className="text-gray-300 text-sm space-y-1">
                <li>• Multi-format support: PDF, images</li>
                <li>• Advanced extraction: Tables, charts</li>
                <li>• Visual grounding capabilities</li>
                <li>• Batch processing support</li>
              </ul>
            </motion.div>

            <motion.div 
              variants={cardVariants} 
              className="group relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
              whileHover={{ y: -5 }}
            >
              <motion.div
                className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl mb-4 shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", damping: 15 }}
              >
                <ChatBubbleLeftRightIcon className="w-6 h-6 text-white" />
              </motion.div>
              <h3 className="text-lg font-semibold text-white mb-2">AI-Powered Chat</h3>
              <ul className="text-gray-300 text-sm space-y-1">
                <li>• Smart context-aware prompts</li>
                <li>• Multiple AI providers support</li>
                <li>• Confidence scoring system</li>
                <li>• Source attribution tracking</li>
              </ul>
            </motion.div>

            <motion.div 
              variants={cardVariants} 
              className="group relative backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
              whileHover={{ y: -5 }}
            >
              <motion.div
                className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl mb-4 shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", damping: 15 }}
              >
                <ChartBarIcon className="w-6 h-6 text-white" />
              </motion.div>
              <h3 className="text-lg font-semibold text-white mb-2">Advanced Analytics</h3>
              <ul className="text-gray-300 text-sm space-y-1">
                <li>• Content structure analysis</li>
                <li>• Semantic chunking intelligence</li>
                <li>• Document comparison tools</li>
                <li>• Export in multiple formats</li>
              </ul>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="p-8 min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/30 to-black"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1.5 }}
      />

      {/* Background Effects */}
      <motion.div
        className="absolute top-40 left-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 2, ease: "easeOut" }}
      />
      <motion.div
        className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl"
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 2.5, ease: "easeOut", delay: 0.5 }}
      />

      <motion.div
        initial="hidden"
        animate="visible"
        variants={staggerContainer}
        className="max-w-7xl mx-auto relative z-10"
      >
        {/* Header with Username */}
        <motion.div className="mb-8" variants={cardVariants}>
          <motion.div
            className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full px-4 py-2 mb-4 backdrop-blur-sm"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
          >
            <UserIcon className="w-4 h-4 text-blue-400" />
            <span className="text-blue-300 text-sm font-medium">
              Welcome, {username}
            </span>
          </motion.div>
          <h1 className="text-3xl font-bold text-white mb-2">Dashboard</h1>
          <p className="text-gray-300">Overview of your processed documents and analytics</p>
        </motion.div>

        {/* Stats Cards */}
        <motion.div variants={staggerContainer} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div 
            variants={cardVariants} 
            className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm mb-1">Total Documents</p>
                <p className="text-2xl font-bold text-white">{documents.length}</p>
              </div>
              <motion.div whileHover={{ scale: 1.1, rotate: 5 }} transition={{ type: "spring", damping: 15 }}>
                <DocumentTextIcon className="w-8 h-8 text-blue-400" />
              </motion.div>
            </div>
          </motion.div>

          <motion.div 
            variants={cardVariants} 
            className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm mb-1">Total Chunks</p>
                <p className="text-2xl font-bold text-white">{totalChunks.toLocaleString()}</p>
              </div>
              <motion.div whileHover={{ scale: 1.1, rotate: 5 }} transition={{ type: "spring", damping: 15 }}>
                <CubeIcon className="w-8 h-8 text-purple-400" />
              </motion.div>
            </div>
          </motion.div>

          <motion.div 
            variants={cardVariants} 
            className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm mb-1">Total Text Length</p>
                <p className="text-2xl font-bold text-white">{(totalTextLength / 1000).toFixed(1)}K</p>
              </div>
              <motion.div whileHover={{ scale: 1.1, rotate: 5 }} transition={{ type: "spring", damping: 15 }}>
                <ChartBarIcon className="w-8 h-8 text-emerald-400" />
              </motion.div>
            </div>
          </motion.div>

          <motion.div 
            variants={cardVariants} 
            className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
            whileHover={{ y: -5 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm mb-1">Avg Complexity</p>
                <p className="text-2xl font-bold text-white">{avgComplexity.toFixed(0)}/100</p>
              </div>
              <motion.div whileHover={{ scale: 1.1, rotate: 5 }} transition={{ type: "spring", damping: 15 }}>
                <ClockIcon className="w-8 h-8 text-orange-400" />
              </motion.div>
            </div>
          </motion.div>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Recent Documents */}
          <motion.div
            variants={cardVariants}
            className="lg:col-span-2 backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6"
          >
            <h2 className="text-xl font-semibold text-white mb-6">Recent Documents</h2>
            <div className="space-y-4">
              {recentDocuments.map((doc, index) => (
                <motion.div
                  key={doc.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1, type: "spring", damping: 25 }}
                  className={`
                    flex items-center justify-between p-4 rounded-xl border cursor-pointer transition-all duration-300 hover:bg-white/5
                    ${selectedDocument?.id === doc.id ? 'bg-blue-500/20 border-blue-500/50' : 'border-white/10'}
                  `}
                  onClick={() => onDocumentSelect(doc)}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center space-x-4">
                    <motion.div whileHover={{ rotate: 5 }} transition={{ type: "spring", damping: 15 }}>
                      <DocumentTextIcon className="w-8 h-8 text-blue-400" />
                    </motion.div>
                    <div>
                      <p className="text-white font-medium">{doc.filename}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-400">
                        <span>{doc.stats?.total_chunks || 0} chunks</span>
                        <span>•</span>
                        <span>{doc.stats?.complexity_score || 0}/100 complexity</span>
                        <span>•</span>
                        <span>{new Date(doc.extraction_time).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <motion.button 
                      onClick={(e) => {
                        e.stopPropagation();
                        onChatClick(doc);
                      }}
                      className="p-2 text-gray-400 hover:text-blue-400 transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      title="Chat with document"
                    >
                      <ChatBubbleLeftRightIcon className="w-5 h-5" />
                    </motion.button>
                    <motion.button 
                      onClick={(e) => {
                        e.stopPropagation();
                        onAnalyticsClick(doc);
                      }}
                      className="p-2 text-gray-400 hover:text-purple-400 transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      title="View analytics"
                    >
                      <ChartBarIcon className="w-5 h-5" />
                    </motion.button>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Quick Upload */}
          <motion.div
            variants={cardVariants}
            className="backdrop-blur-xl bg-white/5 border border-white/10 rounded-2xl p-6"
          >
            <h2 className="text-xl font-semibold text-white mb-6">Quick Upload</h2>
            
            {isUploading ? (
              <div className="border-2 border-blue-400 bg-blue-400/10 rounded-xl p-6 text-center mb-6">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full mx-auto mb-2"
                />
                <p className="text-sm text-blue-300 font-medium">Processing...</p>
              </div>
            ) : (
              <motion.div
                className="border-2 border-dashed rounded-xl p-6 text-center cursor-pointer transition-all duration-300 mb-6 border-white/20 hover:border-white/30 hover:bg-white/5"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => {
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.multiple = true;
                  input.accept = '.pdf,.png,.jpg,.jpeg,.tiff,.bmp';
                  input.onchange = (e) => handleFileUpload([...e.target.files]);
                  input.click();
                }}
              >
                <motion.div
                  initial={{ scale: 0, rotate: -90 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, type: "spring", damping: 20 }}
                >
                  <CloudArrowUpIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                </motion.div>
                <p className="text-sm text-gray-300">
                  Drag files or click to upload
                </p>
              </motion.div>
            )}

            {/* Content Types Distribution */}
            {documents.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-white mb-4">Content Types</h3>
                <div className="space-y-3">
                  {Object.entries(
                    documents.reduce((acc, doc) => {
                      Object.entries(doc.stats?.chunk_types || {}).forEach(([type, count]) => {
                        acc[type] = (acc[type] || 0) + count;
                      });
                      return acc;
                    }, {})
                  ).map(([type, count], index) => (
                    <motion.div 
                      key={type} 
                      className="flex items-center justify-between p-3 bg-white/5 rounded-lg border border-white/10"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1, type: "spring", damping: 25 }}
                      whileHover={{ scale: 1.02 }}
                    >
                      <span className="text-gray-300 capitalize">{type.replace('_', ' ')}</span>
                      <span className="text-white font-medium">{count}</span>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default Dashboard;
