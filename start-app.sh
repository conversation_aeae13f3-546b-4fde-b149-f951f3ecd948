#!/usr/bin/env bash
set -euo pipefail

# -----------------------------------------------------------------------------
# start-app.sh - DocuMind AI Application Startup Script
# -----------------------------------------------------------------------------

# Configuration
PROJECT_ROOT="/home/<USER>/actions-runner/_work/DocuMindAI/DocuMindAI"
BACKEND_PORT=8000
FRONTEND_PORT=3000

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# Change to project root
cd "$PROJECT_ROOT" || {
    echo -e "${RED}[ERROR]${NC} Failed to change to project directory: $PROJECT_ROOT" >&2
    exit 1
}

# Load environment variables from backend/.env if exists
if [ -f backend/.env ]; then
    log "📋 Loading environment variables from backend/.env"
    set -o allexport
    . backend/.env
    set +o allexport
    echo -e "${GREEN}[SUCCESS]${NC} Environment variables loaded"
else
    echo -e "${YELLOW}[WARNING]${NC} No backend/.env file found, continuing without custom environment"
fi

# Kill stale processes on both ports
log "🧹 Cleaning up existing processes..."
for port in $FRONTEND_PORT $BACKEND_PORT; do
    if lsof -i tcp:$port &>/dev/null; then
        log "🔪 Killing existing process on port $port"
        kill -9 $(lsof -t -i tcp:$port) || true
        sleep 2
    else
        log "✅ Port $port is free"
    fi
done



# Create backend startup command - using . instead of source for sh compatibility
BACKEND_CMD="cd backend && . venv/bin/activate && python main.py"

# Create frontend startup command  
FRONTEND_CMD="cd frontend && CI=false npm start"

# Launch both services
log "🚀 Starting DocuMind AI Application..."
log "📊 Backend will be available at: http://localhost:$BACKEND_PORT"
log "🌐 Frontend will be available at: http://localhost:$FRONTEND_PORT"
log "✨ Starting both services..."

# Use concurrently to run both services
exec concurrently \
  --names "BACKEND,FRONTEND" \
  --prefix-colors "blue,green" \
  --prefix "[{name}]" \
  --kill-others \
  --success first \
  "$BACKEND_CMD" \
  "$FRONTEND_CMD"

