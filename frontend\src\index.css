@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar with cyan theme */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.5);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 255, 0.5);
}

/* Firefox scrollbar */
html {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 255, 255, 0.3) rgba(0, 0, 0, 0.5);
}

/* Base styles */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #000000;
  color: #f1f5f9;
  overflow-x: hidden;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Glassmorphism utilities with cyan theme */
.glass {
  background: rgba(0, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 255, 255, 0.15);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.4);
}

/* Custom utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Prose styles for markdown with cyan theme */
.prose {
  color: #e2e8f0;
  max-width: none;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: #f1f5f9;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.prose h1 {
  font-size: 1.875rem;
  font-weight: 700;
}

.prose h2 {
  font-size: 1.5rem;
  font-weight: 600;
}

.prose h3 {
  font-size: 1.25rem;
  font-weight: 600;
}

.prose p {
  margin-top: 1em;
  margin-bottom: 1em;
  line-height: 1.6;
}

.prose ul,
.prose ol {
  margin-top: 1em;
  margin-bottom: 1em;
  padding-left: 1.5rem;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose strong {
  color: #00ffff;
  font-weight: 600;
}

.prose em {
  font-style: italic;
  color: #cbd5e1;
}

.prose code {
  color: #00ffff;
  background-color: rgba(0, 255, 255, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.prose pre {
  background-color: rgba(0, 0, 0, 0.8);
  color: #e5e7eb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin-top: 1em;
  margin-bottom: 1em;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.prose pre code {
  background-color: transparent;
  padding: 0;
  color: inherit;
}

.prose blockquote {
  border-left: 4px solid #00ffff;
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  color: #cbd5e1;
  font-style: italic;
  background-color: rgba(0, 255, 255, 0.05);
  padding: 1rem;
  border-radius: 0.5rem;
}

.prose table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1em;
  margin-bottom: 1em;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 0.5rem;
}

.prose th,
.prose td {
  border: 1px solid rgba(0, 255, 255, 0.2);
  padding: 0.75rem;
  text-align: left;
}

.prose th {
  background-color: rgba(0, 255, 255, 0.1);
  font-weight: 600;
  color: #f1f5f9;
}

.prose td {
  background-color: rgba(0, 0, 0, 0.1);
}

.prose a {
  color: #00ffff;
  text-decoration: underline;
  transition: color 0.2s;
}

.prose a:hover {
  color: #22d3ee;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceGentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Custom focus styles with cyan theme */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.5);
}

/* Loading spinner */
.spinner {
  animation: spin 1s linear infinite;
}

/* Custom checkbox styles with cyan theme */
input[type="checkbox"]:checked {
  background-color: #00ffff;
  border-color: #00ffff;
}

/* Tooltip styles */
.tooltip {
  position: relative;
}

.tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.9);
  color: #00ffff;
  padding: 0.5rem;
  border-radius: 0.25rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  font-size: 0.875rem;
  z-index: 1000;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 255, 255, 0.3);
}

.tooltip:hover::before {
  opacity: 1;
}

/* Custom button animations with cyan theme */
.btn-hover {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.btn-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-hover:hover::before {
  left: 100%;
}

.btn-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
}

/* File upload area styles */
.upload-area {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.upload-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.upload-area.drag-over::before {
  opacity: 1;
}

.upload-area.drag-over {
  border-color: #00ffff;
  background-color: rgba(0, 255, 255, 0.05);
  transform: scale(1.02);
}

/* Progress bar animations with cyan theme */
.progress-bar {
  overflow: hidden;
  position: relative;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 9999px;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

/* Message bubble styles with cyan theme */
.message-bubble {
  max-width: 75%;
  word-wrap: break-word;
  border-radius: 1rem;
  backdrop-filter: blur(10px);
}

.message-bubble.user {
  background: linear-gradient(135deg, #00ffff, #8b5cf6);
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3);
}

.message-bubble.assistant {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Card hover effects with cyan theme */
.card-hover {
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.card-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.05) 0%, transparent 50%, rgba(139, 92, 246, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-hover:hover::before {
  opacity: 1;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 255, 255, 0.1);
}

/* Gradient text with cyan theme */
.gradient-text {
  background: linear-gradient(135deg, #00ffff, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom range slider with cyan theme */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
  background: rgba(0, 0, 0, 0.5);
  height: 0.5rem;
  border-radius: 0.25rem;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(135deg, #00ffff, #8b5cf6);
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 255, 255, 0.4);
}

input[type="range"]::-moz-range-track {
  background: rgba(0, 0, 0, 0.5);
  height: 0.5rem;
  border-radius: 0.25rem;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  background: linear-gradient(135deg, #00ffff, #8b5cf6);
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 255, 255, 0.4);
}

/* Highlight styles for search results with cyan theme */
mark {
  background: linear-gradient(135deg, #00ffff, #22d3ee);
  color: #000;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  box-shadow: 0 2px 4px rgba(0, 255, 255, 0.3);
}

/* Custom scrollbar for specific containers */
.custom-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 255, 255, 0.3) rgba(0, 0, 0, 0.5);
}

.custom-scroll::-webkit-scrollbar {
  width: 8px;
}

.custom-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 4px;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 255, 0.5);
}

/* Floating animation */
.float {
  animation: float 3s ease-in-out infinite;
}

/* Glow effects with cyan theme */
.glow {
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.glow-purple {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
}

.glow-pink {
  box-shadow: 0 0 20px rgba(236, 72, 153, 0.5);
}

/* Backdrop blur utilities */
.backdrop-blur-xs {
  backdrop-filter: blur(2px);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur {
  backdrop-filter: blur(8px);
}

.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
}

.backdrop-blur-xl {
  backdrop-filter: blur(24px);
}

.backdrop-blur-2xl {
  backdrop-filter: blur(40px);
}

.backdrop-blur-3xl {
  backdrop-filter: blur(64px);
}

/* Custom form elements with cyan theme */
input[type="text"],
input[type="email"],
input[type="password"],
textarea,
select {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: #f1f5f9;
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: #00ffff;
  background: rgba(0, 255, 255, 0.05);
  box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.1);
}

input[type="text"]::placeholder,
input[type="email"]::placeholder,
input[type="password"]::placeholder,
textarea::placeholder {
  color: #64748b;
}

/* Custom button styles with cyan theme */
.btn-primary {
  background: linear-gradient(135deg, #00ffff, #8b5cf6);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 255, 255, 0.4);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-secondary {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 255, 255, 0.2);
  color: #f1f5f9;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(0, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 255, 255, 0.2);
}

/* Recharts custom styles with cyan theme */
.recharts-cartesian-grid-horizontal line,
.recharts-cartesian-grid-vertical line {
  stroke: rgba(0, 255, 255, 0.1);
}

.recharts-text {
  fill: #64748b;
}

.recharts-tooltip-wrapper {
  background-color: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 0.5rem;
  backdrop-filter: blur(20px);
}

/* React Toaster custom styles with cyan theme */
.Toastify__toast {
  background-color: rgba(0, 0, 0, 0.9);
  color: #f1f5f9;
  border: 1px solid rgba(0, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.Toastify__toast--success {
  border-left: 4px solid #00ffff;
}

.Toastify__toast--error {
  border-left: 4px solid #ef4444;
}

.Toastify__toast--warning {
  border-left: 4px solid #f59e0b;
}

.Toastify__toast--info {
  border-left: 4px solid #00ffff;
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-black {
    background-color: #000000;
  }
  
  .border-cyan-500\/20 {
    border-color: rgba(0, 255, 255, 0.4);
  }
  
  .text-gray-400 {
    color: #d1d5db;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .float {
    animation: none;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white;
    color: black;
  }
  
  .backdrop-blur-xl,
  .glass,
  .glass-dark {
    background: white !important;
    backdrop-filter: none !important;
  }
  
  .text-white {
    color: black !important;
  }
}